import React from 'react';
import { View, StyleSheet, TouchableOpacity, ActivityIndicator, ScrollView } from 'react-native';
import { useLocalSearchParams, router } from 'expo-router';
import { useTheme } from '@/context/ThemeContext';
import { ThemedText } from '@/components/base/ThemedText';
import { Icon } from '@/components/images/Icon';
import { CommentList } from '@/components/feature/CommentList';
import { BlinkCard } from '@/components/feature/BlinkCard';
import { useTranslation } from 'react-i18next';
import { LinearGradient } from 'expo-linear-gradient';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { useBlinkInterface } from '@/hooks/interfaces/useBlinkInterface';

// Fonction pour transformer les données du blink si nécessaire
function transformBlinkData(blink: any) {
  console.log('=== TRANSFORM BLINK DATA ===');
  console.log('Input blink:', JSON.stringify(blink, null, 2));

  if (!blink) {
    console.log('Blink is null/undefined, returning as is');
    return blink;
  }

  // Si le blink a déjà la structure correcte, le retourner tel quel
  if (blink.profile) {
    console.log('Blink already has profile, returning as is');
    return blink;
  }

  // Si le blink a une structure différente (comme user.Profile), la transformer
  if (blink.user && blink.user.Profile) {
    console.log('Transforming blink.user.Profile to blink.profile');
    const avatarUrl = blink.user.Profile.avatar_url
      ? `${process.env.EXPO_PUBLIC_API_URL}/uploads/${blink.user.Profile.avatar_url}`
      : `${process.env.EXPO_PUBLIC_API_URL}/uploads/default_user.png`;

    const transformed = {
      ...blink,
      profile: {
        userID: blink.user.userID,
        username: blink.user.Profile.username,
        display_name: blink.user.Profile.display_name,
        avatar_url: avatarUrl,
      }
    };

    console.log('Transformed blink:', JSON.stringify(transformed, null, 2));
    return transformed;
  }

  // Créer un profil par défaut si aucune structure n'est trouvée
  console.log('No profile structure found, creating default profile');
  const defaultProfile = {
    ...blink,
    profile: {
      userID: blink.userID || 'unknown',
      username: 'unknown',
      display_name: 'Utilisateur inconnu',
      avatar_url: `${process.env.EXPO_PUBLIC_API_URL}/uploads/default_user.png`,
    }
  };

  console.log('Default profile created:', JSON.stringify(defaultProfile, null, 2));
  return defaultProfile;
}

export default function CommentsPage() {
  const { blinkID } = useLocalSearchParams<{ blinkID: string }>();
  const { colors } = useTheme();
  const { t } = useTranslation();
  const insets = useSafeAreaInsets();

  // Récupérer les données du blink
  const { data: blinkData, isLoading: isBlinkLoading, isError: isBlinkError } = useBlinkInterface(blinkID || '');

  // Debug: Log pour voir la structure des données
  console.log('Blink data from API:', blinkData);

  if (!blinkID) {
    return (
      <View style={[styles.container, { backgroundColor: colors.background }]}>
        <ThemedText style={[styles.errorText, { color: colors.danger }]}>
          {t('comment.invalidBlink')}
        </ThemedText>
      </View>
    );
  }

  const handleGoBack = () => {
    if (router.canGoBack()) {
      router.back();
    } else {
      router.push('/');
    }
  };

  return (
    <View style={[styles.container, { backgroundColor: colors.background }]}>
      {/* Header simple avec bouton retour */}
      <View style={[styles.simpleHeader, { paddingTop: insets.top, backgroundColor: colors.background }]}>
        <TouchableOpacity style={styles.backButton} onPress={handleGoBack}>
          <Icon name="arrow-left" size={24} color={colors.text} />
        </TouchableOpacity>
      </View>

      <ScrollView style={styles.scrollContainer} showsVerticalScrollIndicator={false}>
        {/* Affichage du blink */}
        {isBlinkLoading ? (
          <View style={styles.loadingContainer}>
            <ActivityIndicator size="large" color={colors.accent} />
            <ThemedText style={[styles.loadingText, { color: colors.textSecondary }]}>
              Chargement du blink...
            </ThemedText>
          </View>
        ) : isBlinkError || !blinkData?.data ? (
          <View style={styles.errorContainer}>
            <ThemedText style={[styles.errorText, { color: colors.danger }]}>
              Erreur lors du chargement du blink
            </ThemedText>
          </View>
        ) : (
          <View style={styles.blinkContainer}>
            <BlinkCard blink={transformBlinkData(blinkData.data)} />
          </View>
        )}

        {/* Liste des commentaires */}
        <CommentList blinkID={blinkID} showInput={true} />
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  simpleHeader: {
    paddingHorizontal: 16,
    paddingBottom: 8,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0,0,0,0.1)',
  },
  backButton: {
    padding: 8,
    alignSelf: 'flex-start',
  },
  scrollContainer: {
    flex: 1,
  },
  blinkContainer: {
    paddingHorizontal: 16,
    paddingTop: 8,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 50,
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 50,
  },
  errorText: {
    fontSize: 16,
    textAlign: 'center',
  },
});
