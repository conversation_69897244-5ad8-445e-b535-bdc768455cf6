import React from 'react';
import { View, StyleSheet, ScrollView } from 'react-native';
import { useTheme } from '@/context/ThemeContext';
import { ThemedText } from '@/components/base/ThemedText';
import { CommentList } from '@/components/feature/CommentList';
import { useTranslation } from 'react-i18next';
import { LinearGradient } from 'expo-linear-gradient';
import { useSafeAreaInsets } from 'react-native-safe-area-context';

export default function TestCommentsPage() {
  const { colors } = useTheme();
  const { t } = useTranslation();
  const insets = useSafeAreaInsets();

  // Utiliser un blinkID de test - vous pouvez le changer selon vos données de test
  const testBlinkID = "test-blink-id";

  return (
    <View style={[styles.container, { backgroundColor: colors.background }]}>
      {/* Header */}
      <LinearGradient
        colors={[colors.accent, colors.accentSecondary || colors.accent]}
        style={[styles.header, { paddingTop: insets.top }]}
      >
        <View style={styles.headerContent}>
          <ThemedText style={[styles.headerTitle, { color: colors.white }]}>
            Test - {t('comment.title')}
          </ThemedText>
        </View>
      </LinearGradient>

      {/* Informations de test */}
      <View style={styles.infoContainer}>
        <ThemedText style={[styles.infoText, { color: colors.textSecondary }]}>
          Page de test pour le système de commentaires
        </ThemedText>
        <ThemedText style={[styles.infoText, { color: colors.textSecondary }]}>
          BlinkID de test: {testBlinkID}
        </ThemedText>
      </View>

      {/* Liste des commentaires */}
      <CommentList blinkID={testBlinkID} showInput={true} />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    paddingBottom: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  headerContent: {
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingTop: 16,
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: '600',
  },
  infoContainer: {
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  infoText: {
    fontSize: 14,
    marginBottom: 4,
  },
});
