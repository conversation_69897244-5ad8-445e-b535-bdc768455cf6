import React, { useState } from "react";
import { View, TextInput, StyleSheet, TouchableOpacity, Alert } from "react-native";
import { useTheme } from "@/context/ThemeContext";
import { Icon } from "@/components/images/Icon";
import { ThemedText } from "@/components/base/ThemedText";
import { useTranslation } from "react-i18next";
import { useCreateCommentMutation } from "@/hooks/interfaces/useCommentInterface";

interface CommentInputProps {
  blinkID: string;
  onCommentCreated?: () => void;
  placeholder?: string;
}

export function CommentInput({ blinkID, onCommentCreated, placeholder }: CommentInputProps) {
  const { colors } = useTheme();
  const { t } = useTranslation();
  const [content, setContent] = useState("");
  const [isFocused, setIsFocused] = useState(false);
  
  const createMutation = useCreateCommentMutation();

  const handleSubmit = async () => {
    const trimmedContent = content.trim();
    
    if (!trimmedContent) {
      Alert.alert(t('common.error'), t('comment.emptyError'));
      return;
    }

    if (trimmedContent.length > 1000) {
      Alert.alert(t('common.error'), t('comment.tooLongError'));
      return;
    }

    try {
      await createMutation.mutateAsync({
        blinkID,
        content: trimmedContent
      });

      setContent("");
      onCommentCreated?.();
    } catch (error: any) {
      console.error('Error creating comment:', error);
      console.log('Error message:', error?.message);
      console.log('Full error object:', JSON.stringify(error, null, 2));

      // Vérifier si c'est l'erreur "AlreadyCommented"
      const errorMessage = error?.message || '';
      console.log('Checking error message:', errorMessage);

      if (errorMessage.includes('Comments.AlreadyCommented')) {
        console.log('Detected AlreadyCommented error');
        console.log('Translation for common.error:', t('common.error'));
        console.log('Translation for comment.alreadyCommented:', t('comment.alreadyCommented'));

        // Test avec window.alert pour voir si c'est un problème avec Alert de React Native
        const errorTitle = t('common.error');
        const errorMessage = t('comment.alreadyCommented');
        console.log('About to show alert with:', errorTitle, errorMessage);

        // Essayer les deux méthodes
        Alert.alert(errorTitle, errorMessage);
        window.alert(`${errorTitle}: ${errorMessage}`);

        console.log('Alert called for AlreadyCommented');
      } else {
        console.log('Using generic error message');
        console.log('Translation for comment.createError:', t('comment.createError'));
        Alert.alert(t('common.error'), t('comment.createError'));
        console.log('Alert called for generic error');
      }
    }
  };

  const isSubmitDisabled = !content.trim() || createMutation.isPending;

  return (
    <View style={[styles.container, { backgroundColor: colors.card, borderColor: colors.border }]}>
      <View style={[
        styles.inputContainer, 
        { 
          borderColor: isFocused ? colors.accent : colors.border,
          backgroundColor: colors.background 
        }
      ]}>
        <TextInput
          style={[styles.textInput, { color: colors.text }]}
          value={content}
          onChangeText={setContent}
          placeholder={placeholder || t('comment.placeholder')}
          placeholderTextColor={colors.textSecondary}
          multiline
          maxLength={1000}
          onFocus={() => setIsFocused(true)}
          onBlur={() => setIsFocused(false)}
          textAlignVertical="top"
        />
        
        <TouchableOpacity
          style={[
            styles.submitButton,
            { 
              backgroundColor: isSubmitDisabled ? colors.border : colors.accent,
              opacity: isSubmitDisabled ? 0.5 : 1
            }
          ]}
          onPress={handleSubmit}
          disabled={isSubmitDisabled}
        >
          {createMutation.isPending ? (
            <Icon name="loading" size={20} color={colors.color} />
          ) : (
            <Icon name="send" size={20} color={colors.color} />
          )}
        </TouchableOpacity>
      </View>
      
      {/* Compteur de caractères */}
      <View style={styles.footer}>
        <ThemedText style={[
          styles.characterCount, 
          { 
            color: content.length > 900 ? colors.danger : colors.textSecondary 
          }
        ]}>
          {content.length}/1000
        </ThemedText>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    padding: 16,
    borderRadius: 12,
    borderWidth: 1,
    margin: 16,
  },
  inputContainer: {
    flexDirection: 'row',
    borderWidth: 1,
    borderRadius: 8,
    padding: 12,
    alignItems: 'flex-end',
    gap: 12,
  },
  textInput: {
    flex: 1,
    fontSize: 16,
    minHeight: 40,
    maxHeight: 120,
    textAlignVertical: 'top',
  },
  submitButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
  },
  footer: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    marginTop: 8,
  },
  characterCount: {
    fontSize: 12,
  },
});
